import type { ProcessingSettings } from "@/services/batch-processing/types";
import type { AIAnalysisResult } from "../../index";
import prompts from "../prompts";

export class GroqService {
  private apiKey: string;
  private model: string;

  constructor(settings: ProcessingSettings) {
    this.apiKey = settings.api.apiKey;
    this.model = settings.api.model;
  }

  private parseAIResponse(response: string): AIAnalysisResult {
    const lines = response.split("\n");
    let title = "";
    let description = "";
    let keywords: string[] = [];

    for (const line of lines) {
      if (line.startsWith("1.") || line.toLowerCase().includes("title:")) {
        title = line.replace(/^1\.\s*|title:\s*/i, "").trim();
      } else if (
        line.startsWith("2.") ||
        line.toLowerCase().includes("description:")
      ) {
        description = line.replace(/^2\.\s*|description:\s*/i, "").trim();
      } else if (
        line.startsWith("3.") ||
        line.toLowerCase().includes("keywords:")
      ) {
        const keywordText = line.replace(/^3\.\s*|keywords:\s*/i, "").trim();
        keywords = keywordText
          .split(",")
          .map((k) => k.trim())
          .filter((k) => k.length > 0);
      }
    }

    return { title, description, keywords };
  }

  async analyzeImage(
    imageBase64: string,
    settings: ProcessingSettings
  ): Promise<AIAnalysisResult> {
    // Check base64 image size
    const imageSizeInMB = (imageBase64.length * 3) / 4 / (1024 * 1024);
    if (imageSizeInMB > 4) {
      throw new Error(
        "Base64 image exceeds 4MB limit. Please use a smaller image."
      );
    }

    console.log(`Sending image to Groq API (${imageSizeInMB.toFixed(2)}MB)`);

    // Prepare the prompt for the Groq API
     const prompt = prompts(settings);
     console.log(`Using prompt: ${prompt}`);

    try {
      // Direct API call to Groq
      const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: "user",
              content: [
                { type: "text", text: prompt },
                {
                  type: "image_url",
                  image_url: {
                    url: `data:image/jpeg;base64,${imageBase64}`
                  }
                }
              ]
            }
          ],
          max_tokens: 1024
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Groq API error: ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message || !data.choices[0].message.content) {
        throw new Error('Empty response from Groq API');
      }

      const text = data.choices[0].message.content;

      if (!text.trim()) {
        throw new Error('Empty text response from Groq API');
      }

      const result = this.parseAIResponse(text);
      
      // Validate the result
      if (!result.title || !result.description || !result.keywords.length) {
        throw new Error('Incomplete metadata generated by Groq API');
      }

      return result;
    } catch (error) {
      console.error(error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`${errorMessage}`);
    }
  }
}