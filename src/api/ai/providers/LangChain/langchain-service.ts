import { ChatMistralAI } from "@langchain/mistralai";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HumanMessage, MessageContentImageUrl } from "@langchain/core/messages";
import type { ProcessingSettings } from "@/services/batch-processing/types";
import type { AIAnalysisResult } from "../../index";
import prompts from "../prompts";

export class LangChainService {
  private model: ChatMistralAI | ChatGoogleGenerativeAI;

  constructor(settings: ProcessingSettings) {
    if (settings.api.provider === "Google") {
      this.model = new ChatGoogleGenerativeAI({
        apiKey: settings.api.apiKey,
        model: settings.api.model,
      });
    } else {
      this.model = new ChatMistralAI({
        apiKey: settings.api.apiKey,
        modelName: settings.api.model,
      });
    }
  }

  private parseAIResponse(response: string): AIAnalysisResult {
    const lines = response.split("\n");
    let title = "";
    let description = "";
    let keywords: string[] = [];

    for (const line of lines) {
      if (line.startsWith("1.") || line.toLowerCase().includes("title:")) {
        title = line.replace(/^1\.\s*|title:\s*/i, "").trim();
      } else if (
        line.startsWith("2.") ||
        line.toLowerCase().includes("description:")
      ) {
        description = line.replace(/^2\.\s*|description:\s*/i, "").trim();
      } else if (
        line.startsWith("3.") ||
        line.toLowerCase().includes("keywords:")
      ) {
        const keywordText = line.replace(/^3\.\s*|keywords:\s*/i, "").trim();
        keywords = keywordText
          .split(",")
          .map((k) => k.trim())
          .filter((k) => k.length > 0);
      }
    }

    return { title, description, keywords };
  }

  async analyzeImage(
    imageBase64: string,
    settings: ProcessingSettings
  ): Promise<AIAnalysisResult> {
    // Check base64 image size (Groq limit is 4MB for base64)
    const imageSizeInMB = (imageBase64.length * 3) / 4 / (1024 * 1024);
    if (imageSizeInMB > 4) {
      throw new Error(
        "Base64 image exceeds Groq's 4MB limit. Please use a smaller image."
      );
    }

    console.log(`Sending image to AI model (${imageSizeInMB.toFixed(2)}MB)`);
    // Prepare the prompt for the AI model
    const promptText = prompts(settings);
    console.log("Prompt text:", promptText); // Debug log 
    try {
      const imageContent: MessageContentImageUrl = {
        type: "image_url",
        image_url: `data:image/jpeg;base64,${imageBase64}`,
      };

      const response = await this.model.invoke([
        new HumanMessage({
          content: [{ type: "text", text: promptText }, imageContent],
        }),
      ]);
    if (!response || !response.content) {
        throw new Error('Empty response from Groq API');
      }

      const text = Array.isArray(response.content)
        ? response.content.map(c => c.type === 'text' ? c.text : '').join(' ')
        : String(response.content);

      if (!text.trim()) {
        throw new Error('Empty text response from Groq API');
      }

      const result = this.parseAIResponse(text);
      
      // Validate the result
      if (!result.title || !result.description || !result.keywords.length) {
        throw new Error('Incomplete metadata generated by Groq API');
      }

      return result;
    } catch (error) {
      console.error( error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`${errorMessage}`);
    }
  }
}
