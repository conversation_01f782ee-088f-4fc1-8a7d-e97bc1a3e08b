@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

* {
  box-sizing: border-box;
  margin: 0;
}

:root {
  --background: oklch(0.98 0 301.43);
  --foreground: oklch(0.37 0.03 287.08);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.37 0.03 287.08);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.37 0.03 287.08);
  --primary: oklch(0.61 0.08 299.73);
  --primary-foreground: oklch(0.98 0 301.43);
  --secondary: oklch(0.9 0.03 300.24);
  --secondary-foreground: oklch(0.37 0.03 287.08);
  --muted: oklch(0.89 0.01 299.78);
  --muted-foreground: oklch(0.53 0.04 290.79);
  --accent: oklch(0.79 0.08 359.94);
  --accent-foreground: oklch(0.34 0.04 1.76);
  --destructive: oklch(0.63 0.16 22.67);
  --destructive-foreground: oklch(0.98 0 301.43);
  --border: oklch(0.84 0.02 300.14);
  --input: oklch(0.93 0.01 301.28);
  --ring: oklch(0.61 0.08 299.73);
  --chart-1: oklch(0.61 0.08 299.73);
  --chart-2: oklch(0.79 0.08 359.94);
  --chart-3: oklch(0.73 0.07 169.87);
  --chart-4: oklch(0.85 0.09 76.83);
  --chart-5: oklch(0.79 0.06 258.08);
  --sidebar: oklch(0.96 0.01 301.35);
  --sidebar-foreground: oklch(0.37 0.03 287.08);
  --sidebar-primary: oklch(0.61 0.08 299.73);
  --sidebar-primary-foreground: oklch(0.98 0 301.43);
  --sidebar-accent: oklch(0.79 0.08 359.94);
  --sidebar-accent-foreground: oklch(0.34 0.04 1.76);
  --sidebar-border: oklch(0.87 0.02 302.17);
  --sidebar-ring: oklch(0.61 0.08 299.73);
  --font-sans: Geist, sans-serif;
  --font-serif: "Lora", Georgia, serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-sm:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow-md:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
  --shadow-lg:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
  --shadow-xl:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
}

.dark {
  --background: oklch(0.22 0.02 292.85);
  --foreground: oklch(0.91 0.02 293.56);
  --card: oklch(0.25 0.03 292.73);
  --card-foreground: oklch(0.91 0.02 293.56);
  --popover: oklch(0.25 0.03 292.73);
  --popover-foreground: oklch(0.91 0.02 293.56);
  --primary: oklch(0.71 0.08 302.05);
  --primary-foreground: oklch(0.22 0.02 292.85);
  --secondary: oklch(0.46 0.05 295.56);
  --secondary-foreground: oklch(0.91 0.02 293.56);
  --muted: oklch(0.26 0.03 294.84);
  --muted-foreground: oklch(0.7 0.03 300.06);
  --accent: oklch(0.32 0.03 308.61);
  --accent-foreground: oklch(0.83 0.05 275.61);
  --destructive: oklch(0.69 0.14 21.46);
  --destructive-foreground: oklch(0.22 0.02 292.85);
  --border: oklch(0.31 0.04 293.34);
  --input: oklch(0.28 0.03 291.27);
  --ring: oklch(0.71 0.08 302.05);
  --chart-1: oklch(0.71 0.08 302.05);
  --chart-2: oklch(0.84 0.07 2.67);
  --chart-3: oklch(0.73 0.07 169.87);
  --chart-4: oklch(0.85 0.09 76.83);
  --chart-5: oklch(0.79 0.06 258.08);
  --sidebar: oklch(0.2 0.02 293.66);
  --sidebar-foreground: oklch(0.91 0.02 293.56);
  --sidebar-primary: oklch(0.71 0.08 302.05);
  --sidebar-primary-foreground: oklch(0.22 0.02 292.85);
  --sidebar-accent: oklch(0.32 0.03 308.61);
  --sidebar-accent-foreground: oklch(0.84 0.07 2.67);
  --sidebar-border: oklch(0.28 0.03 291.27);
  --sidebar-ring: oklch(0.71 0.08 302.05);
  --font-sans: Geist, sans-serif;
  --font-serif: "Lora", Georgia, serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --radius: 0.5rem;

  --shadow-2xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-xs: 1px 2px 5px 1px hsl(0 0% 0% / 0.03);
  --shadow-sm:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 1px 2px 0px hsl(0 0% 0% / 0.06);
  --shadow-md:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 2px 4px 0px hsl(0 0% 0% / 0.06);
  --shadow-lg:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 4px 6px 0px hsl(0 0% 0% / 0.06);
  --shadow-xl:
    1px 2px 5px 1px hsl(0 0% 0% / 0.06), 1px 8px 10px 0px hsl(0 0% 0% / 0.06);
  --shadow-2xl: 1px 2px 5px 1px hsl(0 0% 0% / 0.15);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}


  :root {
    --scrollbar-thumb: var(--scrollbar-thumb-color, var(--border));
    --scrollbar-track: var(--scrollbar-track-color, transparent);
    --scrollbar-height: var(--scrollbar-height-size, 7px);
    --scrollbar-heighty: var(--scrollbar-height-size, 7px);
    --scrollbar-radius: var(--scrollbar-radius-size, 4px);
    --scrollbar-radiusy: var(--scrollbar-radius-size, 8px);
  }


/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  scroll-behavior: smooth;
  height: var(--scrollbar-height);
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  margin: 0 4px;
  border-radius: var(--scrollbar-radius);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: var(--scrollbar-radius);
  transition: background-color 0.3s ease;
}

/* Hide scrollbar when not hovering */
.hide-scrollbar::-webkit-scrollbar-thumb {
  background: transparent;
}

/* Show scrollbar when hovering */
.show-scrollbar::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
}
/* Auto-selection animation - smoother version */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 var(--highlight-color);
    transform: scale(1);
  }
  20% {
    box-shadow: 0 0 0 4px var(--highlight-color);
    transform: scale(1.02);
  }
  50% {
    box-shadow: 0 0 0 8px var(--highlight-color-fade);
    transform: scale(1.01);
  }
  80% {
    box-shadow: 0 0 0 4px var(--highlight-color-fade);
    transform: scale(1.005);
  }
  100% {
    box-shadow: 0 0 0 0 var(--highlight-color-fade);
    transform: scale(1);
  }
}

.auto-selected {
  animation: highlight-pulse 1.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.scrollbartaxt{
  scrollbar-width: none;
}
