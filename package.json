{"name": "tagpix-ai", "private": true, "version": "6.0.0", "type": "module", "main": "dist-electron/main.js", "description": " AI powered metadata generator", "author": "AR Rifat", "scripts": {"dev": "npm-run-all --parallel dev:r dev:e", "dev:r": "vite", "dev:e": "npm run transpile:electron && cross-env NODE_ENV=development electron .", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "transpile:electron": "tsc --project src/electron/tsconfig.json", "dist:mac": "npm run transpile:electron && npm run build && electron-builder --mac --arm64", "dist:win": "npm run transpile:electron && npm run build && electron-builder --win --x64 --config electron-builder.json", "dist:linux": "npm run transpile:electron && npm run build && electron-builder --linux --x64", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@ai-sdk/google": "^1.2.14", "@ai-sdk/groq": "^1.2.8", "@ai-sdk/mistral": "^1.2.7", "@emnapi/runtime": "^1.4.3", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.0.1", "@langchain/community": "^0.3.42", "@langchain/core": "^0.3.49", "@langchain/google-genai": "^0.2.5", "@langchain/groq": "^0.2.2", "@langchain/mistralai": "^0.2.0", "@mistralai/mistralai": "^1.6.0", "@radix-ui/react-toast": "^1.2.13", "@types/ffmpeg-static": "^3.0.3", "@types/fluent-ffmpeg": "^2.1.27", "ai": "^4.3.13", "camelcase": "^8.0.0", "date-fns": "^4.1.0", "decamelize": "^6.0.0", "dotenv": "^16.5.0", "electron-packager": "^17.1.2", "electron-store": "^10.0.1", "exiftool-vendored": "^30.3.0", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "imagescript": "^1.3.0", "input-otp": "^1.4.2", "jimp": "^1.6.0", "langchain": "^0.3.19", "node-fetch": "^2.7.0", "react-scroll-into-view": "^2.1.3", "scroll-into-view-if-needed": "^3.1.0", "sonner": "^2.0.3", "styled-components": "^6.1.18", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.9", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.26.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/vite": "^4.1.5", "@types/node": "^22.15.3", "@types/node-fetch": "^2.6.12", "@types/react": "^19.0.10", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react": "^4.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cross-env": "^7.0.3", "electron": "^36.1.0", "electron-builder": "^26.0.12", "embla-carousel-react": "^8.5.2", "eslint": "^9.26.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "lucide-react": "^0.485.0", "motion": "^12.9.4", "next-themes": "^0.4.6", "npm-run-all": "^4.1.5", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-minimal-tooltip": "^0.2.4", "react-resizable-panels": "^3.0.0", "react-router-dom": "^7.5.3", "react-scan": "^0.0.44", "react-tooltip": "^5.28.1", "recharts": "^2.15.1", "terser": "^5.39.0", "vite": "^6.3.4"}}